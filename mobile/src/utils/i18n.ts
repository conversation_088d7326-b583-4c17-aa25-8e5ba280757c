import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import { I18nManager } from "react-native";

import en from "../locales/en.json";
import he from "../locales/he.json";

export const languageTag: string = "he";
const isRTL = languageTag === "he" ? true : false;

const resources = {
  en: { translation: en },
  he: { translation: he },
};

if (I18nManager.isRTL !== isRTL) {
  I18nManager.allowRTL(isRTL);
  I18nManager.forceRTL(isRTL);
  // You may need to reload the app for layout direction to take effect
}

i18n.use(initReactI18next).init({
  resources,
  lng: languageTag,
  fallbackLng: "en",
  interpolation: {
    escapeValue: false,
  },
});

export default i18n;
