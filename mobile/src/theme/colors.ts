// theme/colors.ts
export const colors = {
  light: {
    tipBoxBackground: "#fff7e6",
    tipBoxText: "#7a5b00",
    tipBoxIcon: "#b89c1d",
    tipBoxBorder: "#e2b77a",

    dayTitle: "black",
    CollapsedDayCardBackground: "white",
    CollapsedDayCardLowerText: "gray",
    exerciseCount: "gray",

    exerciseRow: "#F0F8FF",
    exerciseBorder: "#cfd8e3",

    statPillRest: "#e3f7e5",
    statPillReps: "#ffe7d5",
    statPillSets: "rgba(239, 246, 255, 0.7)",

    statRestText: "#46805a",
    statRepsText: "#a9880a",
    statSetsText: "#4A90E2",

    exerciseDetails: "#F3F6FA",

    exerciseDetailsInnerContainerBg: "white",
    instructionsContainerBg: "#fff7e6",
    instructionsTextHeader: "#b89c1d",
    instructionsText: "#7a5b00",

    expandedStatPillRestText: "#27613b",
    expandedStatPillRestBorder: "#e3f7e5",
    expandedStatPillRestBg: "#f6fdfa",

    expandedStatPillRepsText: "#a35b00",
    expandedStatPillRepsBorder: "#ffe7d5",
    expandedStatPillRepsBg: "#fff8f1",

    expandedStatPillSetsText: "#4A90E2",
    expandedStatPillSetsBorder: "#4A90E2",
    expandedStatPillSetsBg: "rgba(239, 246, 255, 0.7)",

    expandedExerciseDetailsName: "#22223b",

    exerciseVideoButttonBg: "rgba(239, 246, 255, 0.7)",
    exerciseVideoButttonText: "#4A90E2",
    noTrainingPlanText: "#6b7280",

    themePrimary: "#e5e7eb",
    themeSecondary: "black",
    themePrimaryBackground: "white",
    background: "#F3F4F6",
    text: "#000000",
    primary: "#FFFFFF",
    Buttonprimary: "#3B82F6",
    Buttonsecondary: "#F9FAFB",
    ButtonText: "#ffffff",
    cardBackground: "#FFFFFF",
    Heading: "#3B82F6",
    userName: "#6B7280",
    WeightCard: "#D1FAE5",
    BoduFatCard: "#DBEAFE",
    MeterTitle: "#4B5563",
    FoodItemName: "#2D3748",
    ServingItem: "rgba(239, 246, 255, 0.7)",
    FinsihButton: "#EF4444",
  },
  dark: {
    tipBoxBackground: "#412a23",
    tipBoxText: "#f9d04a",
    tipBoxIcon: "#f3e38a",
    tipBoxBorder: "#6a3416",

    dayTitle: "white",
    CollapsedDayCardBackground: "#1f2937",
    CollapsedDayCardLowerText: "#9ca1a7",
    exerciseCount: "#9ca1a7",

    exerciseRow: "#1F2937",
    exerciseBorder: "#4b5563",

    statPillRest: "#29532d",
    statPillReps: "#924011",
    statPillSets: "#5c3c87",

    statRestText: "#92cca6",
    statRepsText: "#f5d456",
    statSetsText: "#e9d5ff",

    exerciseDetails: "#374151",

    instructionsContainerBg: "#392a27",
    exerciseDetailsInnerContainerBg: "#1f2937",
    instructionsTextHeader: "#d9b78a",
    instructionsText: "#c78736",

    expandedStatPillRestText: "#74d5a1",
    expandedStatPillRestBorder: "#29532d",
    expandedStatPillRestBg: "#224231",

    expandedStatPillRepsText: "#fad34b",
    expandedStatPillRepsBorder: "#924011",
    expandedStatPillRepsBg: "#532d1a",

    expandedStatPillSetsText: "#bea3d2",
    expandedStatPillSetsBorder: "#5c3c87",
    expandedStatPillSetsBg: "#422f67",

    expandedExerciseDetailsName: "#ffffff",

    exerciseVideoButttonBg: "#5c3c87",
    exerciseVideoButttonText: "#e9cee4",
    noTrainingPlanText: "rgb(156 163 175)",

    themePrimary: "rgba(88, 28, 135, 1)",
    themeSecondary: "rgb(156 163 175)",
    themePrimaryBackground: "#1f2937",
    background: "#111827",
    text: "#ffffff",
    Buttonsecondary: "#374151", // Updated to Gray-700
    primary: "#C4B5FD", // Updated to Purple-300
    Buttonprimary: "#6D28D9", // Updated to Purple-700
    ButtonText: "#ffffff",
    cardBackground: "#1F2937",
    Heading: "#FFFFFF",
    userName: "#FFFFFF",
    // Updated to match new-frontend color scheme
    WeightCard: "#EF4444", // Red for weight card
    BoduFatCard: "#EF4444", // Red for body fat card
    MeterTitle: "#FFFFFF",
    ServingItem: "rgba(239, 246, 255,0.3)",
    FinsihButton: "#EF4444",
    FoodItemName: "#FFFFFF",
    // New purple theme colors for meal plan
    mealPlanCardBg: "#1F2937", // Dark gray background
    mealPlanBorder: "#4C1D95", // Purple-900 border
    mealCardBg: "#1F2937", // Dark gray background
    mealCardBorder: "#6D28D9", // Purple-700 border
    iconContainerBg: "#4C1D95", // Purple-900 background
    iconColor: "#C4B5FD", // Purple-300 icon color
    categoryBg: "#374151", // Gray-700 background
    categoryBorder: "#6D28D9", // Purple-700 border
    optionItemBg: "rgba(76, 29, 149, 0.2)", // Purple-900 with opacity
    optionItemBorder: "#8B5CF6", // Purple-500 border

    // MacroDisplay colors
    macroProteinBg: "rgba(30, 58, 138, 0.2)", // Blue-900 with opacity
    macroProteinBorder: "#3B82F6", // Blue-500
    macroProteinText: "#60A5FA", // Blue-400
    macroCarbsBg: "rgba(146, 64, 14, 0.2)", // Amber-900 with opacity
    macroCarbsBorder: "#F59E0B", // Amber-500
    macroCarbsText: "#F59E0B", // Amber-500
    macroFatsBg: "rgba(6, 78, 59, 0.2)", // Green-900 with opacity
    macroFatsBorder: "#10B981", // Green-500
    macroFatsText: "#10B981", // Green-500
    macroCaloriesBg: "rgba(153, 27, 27, 0.2)", // Red-900 with opacity
    macroCaloriesBorder: "#EF4444", // Red-500
    macroCaloriesText: "#EF4444", // Red-500
  },
};
