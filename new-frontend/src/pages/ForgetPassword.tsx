import React, { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import { Mail, Send } from "lucide-react";
import { useForgotPasswordMutation } from "../api/services/OTP/OtpService";

const ForgetPassword = () => {
  const navigate = useNavigate();
  const [forgotPassword, { isLoading }] = useForgotPasswordMutation();

  const [email, setEmail] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Effect to clear messages after 3 seconds
  useEffect(() => {
    let timeoutId: ReturnType<typeof setTimeout>;

    if (error || success) {
      timeoutId = setTimeout(() => {
        setError(null);
        setSuccess(null);
      }, 3000);
    }

    // Cleanup function to clear timeout
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [error, success]);

  const backendToFrontendErrorMap: Record<string, string> = {
    "email should not be empty": "יש להזין כתובת אימייל",
    "email must be an email": "כתובת האימייל אינה תקינה",
    "No account found with this email address":
      "לא נמצא חשבון עם כתובת האימייל הזו",
    "If an account with this email exists, a password reset OTP has been sent":
      "אם קיים חשבון עם כתובת האימייל הזו, נשלח קוד לאיפוס סיסמה",
    "Email configuration error": "שגיאת הגדרות דואר אלקטרוני",
    "Failed to process password reset request": "הבקשה לאיפוס הסיסמה נכשלה",
  };

  const getForgotPasswordError = (backendMessage: string) =>
    backendToFrontendErrorMap[backendMessage] || "אירעה שגיאה בלתי צפויה";

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccess(null);

    try {
      await forgotPassword({ email }).unwrap();

      setSuccess("הוראות לאיפוס הסיסמה נשלחו לאימייל שלך.");

      setTimeout(() => {
        navigate(`/reset-password?email=${email}`, { state: { email } });
      }, 2000);
    } catch (error) {
      setError(
        getForgotPasswordError(
          (error as { data: { message: string } })?.data?.message || ""
        )
      );
      console.error("Error:", error);
    }
  };

  return (
    <div className="min-h-screen bg-[#111827] flex items-center justify-center p-4">
      <div className="max-w-md w-full space-y-8 bg-[#1f2937] p-8 rounded-xl shadow-lg border-2 border-[#5c3c87] text-white">
        <div className="text-center">
          <h1 className="text-4xl font-bold mb-2">ראדה-AI</h1>
          <p className="text-lg text-[#938d79]">שחזור סיסמה</p>
        </div>

        {/* Error Message */}
        {error && (
          <div
            dir="rtl"
            className="bg-red-50 border border-red-400 text-red-700 px-4 py-3 rounded relative transition-all duration-300 ease-in-out"
            role="alert"
          >
            <span className="block sm:inline">{error}</span>
          </div>
        )}

        {/* Success Message */}
        {success && (
          <div
            dir="rtl"
            className="bg-green-50 border border-green-400 text-green-700 px-4 py-3 rounded relative transition-all duration-300 ease-in-out"
            role="alert"
          >
            <span className="block sm:inline">{success}</span>
          </div>
        )}

        <form onSubmit={handleSubmit} className="mt-8 space-y-6">
          <div className="space-y-4">
            <div className="relative">
              <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                <Mail className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="email"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full pr-10 pl-3 py-2 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 text-black text-right"
                placeholder="הכנס כתובת אימייל"
                autoComplete="email"
              />
            </div>
          </div>

          <button
            type="submit"
            disabled={isLoading}
            className={`w-full flex justify-center items-center gap-2 px-4 py-3 bg-[#5c3c87] text-white rounded-lg hover:bg-[#664296] transition-colors duration-200 font-semibold ${
              isLoading ? "opacity-70 cursor-not-allowed" : ""
            }`}
          >
            <Send className="h-5 w-5" />
            {isLoading ? "שולח..." : "שלח קוד"}
          </button>

          <div className="text-center mt-4 space-y-2">
            <Link
              to="/login"
              className="hover:underline text-gray-400 font-medium"
            >
              חזרה להתחברות
            </Link>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ForgetPassword;
