import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { Lock, Shield, Eye, EyeOff } from "lucide-react";
import {
  useForgotPasswordMutation,
  useResetPasswordMutation,
  useVerifyOtpMutation,
} from "../api/services/OTP/OtpService";

const ResetPassword: React.FC = () => {
  const [resetPassword] = useResetPasswordMutation();
  const [forgotPassword] = useForgotPasswordMutation();
  const [verifyOtp] = useVerifyOtpMutation();

  const [otp, setOtp] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [resendDisabled, setResendDisabled] = useState(false);
  const [countdown, setCountdown] = useState(30);

  const navigate = useNavigate();
  const location = useLocation();

  const emailFromState = location.state?.email || "";
  const emailFromQuery =
    new URLSearchParams(location.search).get("email") || "";
  const email = emailFromState || emailFromQuery;

  // Redirect if email is missing
  useEffect(() => {
    if (!email) {
      setError("לא צוינה כתובת אימייל לאיפוס הסיסמה.");
      // Optionally: navigate("/forgot-password");
    }
  }, [email]);

  // Error/success reset
  useEffect(() => {
    let timeoutId: ReturnType<typeof setTimeout>;
    if (error || success) {
      timeoutId = setTimeout(() => {
        setError(null);
        if (success) {
          setSuccess(null);
        }
      }, 3000);
    }
    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [error, success]);

  // Resend OTP countdown
  useEffect(() => {
    if (resendDisabled) {
      const interval = setInterval(() => {
        setCountdown((prev) => {
          if (prev === 1) {
            clearInterval(interval);
            setResendDisabled(false);
            return 30;
          }
          return prev - 1;
        });
      }, 1000);
      return () => clearInterval(interval);
    }
  }, [resendDisabled]);

  const backendToFrontendErrorMap: Record<string, string> = {
    "email should not be empty": "יש להזין כתובת אימייל",
    "email must be an email": "כתובת האימייל אינה תקינה",
    "otp should not be empty": "יש להזין קוד אימות",
    "newPassword should not be empty": "יש להזין סיסמה חדשה",
    "Password must be at least 8 characters long":
      "הסיסמה חייבת להכיל לפחות 8 תווים",
    "Invalid OTP": "קוד האימות שגוי",
    "OTP has expired": "תוקף קוד האימות פג",
    "User not found": "המשתמש לא נמצא",
    "Failed to update password in Firebase": "עדכון הסיסמה ב-Firebase נכשל",
  };

  const validatePassword = (password: string): string | null => {
    if (!password) {
      return "יש להזין סיסמה";
    }
    if (password.length < 8) {
      return "הסיסמה חייבת להכיל לפחות 8 תווים";
    }
    if (!/[A-Z]/.test(password)) {
      return "הסיסמה חייבת להכיל לפחות אות גדולה אחת באנגלית";
    }
    if (!/[a-z]/.test(password)) {
      return "הסיסמה חייבת להכיל לפחות אות קטנה אחת באנגלית";
    }
    if (!/[0-9]/.test(password)) {
      return "הסיסמה חייבת להכיל לפחות ספרה אחת";
    }
    if (!/[!@#$%^&*]/.test(password)) {
      return "הסיסמה חייבת להכיל לפחות תו מיוחד אחד (!@#$%^&*)";
    }
    return null;
  };

  const getResetPassError = (backendMessage: string) =>
    backendToFrontendErrorMap[backendMessage] || "אירעה שגיאה בלתי צפויה";

  // Handle form submit
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccess(null);

    if (!email) {
      setError("לא צוינה כתובת אימייל לאיפוס הסיסמה.");
      return;
    }

    const passwordError = validatePassword(password);

    if (passwordError) {
      setError(passwordError);
      setIsSubmitting(false);

      return;
    }

    if (password !== confirmPassword) {
      setError("הסיסמאות אינן תואמות");
      return;
    }

    setIsSubmitting(true);
    try {
      await verifyOtp({ otp }).unwrap();

      await resetPassword({ email, otp, newPassword: password }).unwrap();

      setSuccess("הסיסמה אופסה בהצלחה!");
      setTimeout(() => navigate("/login"), 2000);
    } catch (err) {
      setError(
        getResetPassError(
          (err as { data: { message: string } })?.data?.message || ""
        )
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle resend OTP
  const handleResendOtp = async () => {
    setResendDisabled(true);
    try {
      await forgotPassword({ email }).unwrap();
      setSuccess("קוד האימות נשלח מחדש בהצלחה!");
      setCountdown(30);
    } catch (error) {
      console.error(error);
      setError("שליחת קוד האימות נכשלה. אנא נסה שוב.");
      setResendDisabled(false);
    }
  };

  const togglePasswordVisibility = (field: "password" | "confirmPassword") => {
    if (field === "password") {
      setShowPassword((prev) => !prev);
    } else {
      setShowConfirmPassword((prev) => !prev);
    }
  };

  return (
    <div className="min-h-screen bg-[#111827] flex items-center justify-center p-4">
      <div className="max-w-md w-full space-y-8 bg-[#1f2937] p-8 rounded-xl shadow-lg border-2 border-[#5c3c87] text-white">
        <div className="text-center">
          <h1 className="text-4xl font-bold mb-2">ראדה-AI</h1>
          <p className="text-lg text-[#938d79]">איפוס סיסמה</p>
        </div>

        {error && (
          <div
            dir="rtl"
            className="bg-red-50 border border-red-400 text-red-700 px-4 py-3 rounded"
          >
            {error}
          </div>
        )}

        {success && (
          <div
            dir="rtl"
            className="bg-green-50 border border-green-400 text-green-700 px-4 py-3 rounded"
          >
            {success}
          </div>
        )}

        <form onSubmit={handleSubmit} className="mt-8 space-y-6">
          <div className="space-y-4">
            <div className="relative">
              <Shield className="absolute right-3 top-3 h-5 w-5 text-gray-400" />
              <input
                type="text"
                required
                value={otp}
                onChange={(e) => setOtp(e.target.value)}
                className="text-right w-full pr-10 pl-3 py-2 border-2 border-gray-200 rounded-lg text-black"
                placeholder="הזן קוד אימות שנשלח לאימייל"
                disabled={isSubmitting}
              />
            </div>
            <div className="relative">
              <Lock className="absolute right-3 top-3 h-5 w-5 text-gray-400" />
              <input
                type={showPassword ? "text" : "password"}
                required
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="text-right w-full pl-10 pr-10 py-2 border-2 border-gray-200 rounded-lg text-black"
                placeholder="סיסמה חדשה"
                disabled={isSubmitting}
              />
              <button
                type="button"
                onClick={() => togglePasswordVisibility("password")}
                className="absolute left-3 top-3"
                tabIndex={-1}
              >
                {showPassword ? (
                  <EyeOff className="h-5 w-5 text-gray-400" />
                ) : (
                  <Eye className="h-5 w-5 text-gray-400" />
                )}
              </button>
            </div>
            <div className="relative">
              <Lock className="absolute right-3 top-3 h-5 w-5 text-gray-400" />
              <input
                type={showConfirmPassword ? "text" : "password"}
                required
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="text-right w-full pr-10 pl-10 py-2 border-2 border-gray-200 rounded-lg text-black"
                placeholder="אשר סיסמה חדשה"
                disabled={isSubmitting}
              />
              <button
                type="button"
                onClick={() => togglePasswordVisibility("confirmPassword")}
                className="absolute left-3 top-3"
                tabIndex={-1}
              >
                {showConfirmPassword ? (
                  <EyeOff className="h-5 w-5 text-gray-400" />
                ) : (
                  <Eye className="h-5 w-5 text-gray-400" />
                )}
              </button>
            </div>
          </div>
          <button
            type="submit"
            disabled={
              isSubmitting || !otp || !password || !confirmPassword || !email
            }
            className="w-full px-4 py-3 bg-[#5c3c87] text-white rounded-lg hover:bg-[#664296] "
          >
            {isSubmitting ? "מאפס..." : "אפס סיסמה"}
          </button>
          <button
            type="button"
            onClick={handleResendOtp}
            disabled={resendDisabled || isSubmitting}
            className="w-full mt-3 px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600"
          >
            {resendDisabled
              ? `שלח קוד מחדש בעוד ${countdown}שניות`
              : "שלח קוד מחדש"}
          </button>
        </form>
      </div>
    </div>
  );
};

export default ResetPassword;
