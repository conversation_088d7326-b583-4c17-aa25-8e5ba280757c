import React, { useEffect, useState } from "react";
import {
  UserPlus,
  Search,
  Filter,
  Edit2,
  Trash2,
  MoreVertical,
  Phone,
  Loader,
  Check,
} from "lucide-react";
import { TrainerAnalyticsDashboard } from "./TrainerAnalyticsDashboard";
import {
  useAddNewTrainerMutation,
  useAdminGetAllTrainersQuery,
  useDeleteTrainerMutation,
  useEditTrainerMutation,
} from "../../api/services/Admin/AdminService";
import { Trainer } from "../../data/trainers";
import { ErrorAlert } from "../training/ErrorAlert";

export const AdminTrainers: React.FC = () => {
  const { data, refetch } = useAdminGetAllTrainersQuery({});

  const [trainers, setTrainers] = useState<Trainer[]>([]);

  useEffect(() => {
    if (data && data.data) {
      setTrainers(data.data);
    }
  }, [data]);

  useEffect(() => {
    refetch();
  }, [refetch]);

  const [searchQuery, setSearchQuery] = useState("");

  const [showNewTrainerForm, setShowNewTrainerForm] = useState(false);
  const [selectedTrainer, setSelectedTrainer] = useState<Trainer | null>(null);

  const handleTrainerClick = (trainer: Trainer) => {
    setSelectedTrainer(trainer);
  };

  const [addNewTrainer, { error, isLoading }] = useAddNewTrainerMutation();
  const [editTrainer, { isLoading: isEditingLoading }] =
    useEditTrainerMutation();

  const [newTrainerName, setNewTrainerName] = useState("");
  const [newTrainerEmail, setNewTrainerEmail] = useState("");
  const [newTrainerRole, setNewTrainerRole] = useState("senior_trainer");
  const [newTrainerCountryCode, setNewTrainerCountryCode] = useState("+972");
  const [newTrainerMobileNumber, setNewTrainerMobileNumber] = useState("");

  const [isEditing, setIsEditing] = useState(false);
  const [trainerBeingEdited, setTrainerBeingEdited] = useState<Trainer | null>(
    null
  );

  const HandleTrainerSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    const fullMobileNumber = newTrainerMobileNumber
      ? `${newTrainerCountryCode}${
          newTrainerMobileNumber.startsWith("0")
            ? newTrainerMobileNumber.substring(1)
            : newTrainerMobileNumber
        }`
      : "";

    const trainerData = {
      name: newTrainerName,
      email: newTrainerEmail,
      role: newTrainerRole,
      mobileNumber: fullMobileNumber,
    };

    try {
      if (isEditing && trainerBeingEdited) {
        const resp = await editTrainer({
          body: trainerData,
          trainerId: trainerBeingEdited.id,
        }).unwrap();

        setTrainers((prev) =>
          prev.map((t) =>
            t.id === trainerBeingEdited.id ? { ...t, ...resp.data } : t
          )
        );
      } else {
        const resp = await addNewTrainer(trainerData).unwrap();

        setTrainers((prev) => [...prev, resp.data]);
      }

      // Reset state
      setShowNewTrainerForm(false);
      setIsEditing(false);
      setTrainerBeingEdited(null);
      setNewTrainerName("");
      setNewTrainerEmail("");
      setNewTrainerMobileNumber("");
      setNewTrainerCountryCode("+972");
      setNewTrainerRole("senior_trainer");
    } catch (error) {
      console.error("Failed to submit trainer:", error);
    }
  };

  const [deleteTrainer] = useDeleteTrainerMutation();

  const HandleTrainerDelete = async (
    e: React.MouseEvent<HTMLButtonElement>,
    trainerId: string
  ) => {
    e.stopPropagation();
    e.preventDefault();

    try {
      await deleteTrainer(trainerId).unwrap();

      setTrainers((prev) => prev.filter((t) => t.id !== trainerId));
    } catch (error) {
      console.error("Failed to add trainer:", error);
    }
  };

  if (selectedTrainer) {
    return (
      <TrainerAnalyticsDashboard
        trainer={selectedTrainer}
        onBack={() => setSelectedTrainer(null)}
      />
    );
  }

  //   const filteredTrainers = trainers.filter((trainer) => {
  //     const query = searchQuery.toLowerCase();
  //     return (
  //       trainer.name.toLowerCase().includes(query) ||
  //       trainer.email.toLowerCase().includes(query)
  //     );
  //   });

  return (
    <div className="space-y-6" dir="rtl">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-200">ניהול מאמנים </h2>

        <button
          onClick={() => setShowNewTrainerForm(true)}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
        >
          <UserPlus className="h-5 w-5" />
          <span>הוסף מאמן חדש</span>
        </button>
      </div>

      {/* Search and Filter */}
      <div className="flex gap-4">
        <div className="flex-1 relative">
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
          <input
            type="text"
            placeholder="חפש מאמנים..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pr-10 pl-4 py-2 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 text-right"
          />
        </div>

        <button className="px-4 py-2 bg-white border-2 border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200 flex items-center gap-2">
          <Filter className="h-5 w-5 text-gray-600" />
          <span>סינונים</span>
        </button>
      </div>

      {/* Trainers Table */}
      <div className="bg-white rounded-xl shadow-lg overflow-hidden border-2 border-gray-200" style={{ flexDirection: "row-reverse"}}>
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                פעולות
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                ביצועים
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                מתאמנים
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                סטטוס
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                תפקיד
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                מאמן
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {trainers?.length > 0 &&
              trainers.map((trainer) => {
                return (
                  <tr
                    key={trainer.id}
                    className="hover:bg-gray-50 cursor-pointer"
                    onClick={() => handleTrainerClick(trainer)}
                  >
                    <td className="px-6 py-4 whitespace-nowrap text-left text-sm font-medium">
                      <div className="flex items-center justify-start gap-2">
                        <button className="p-1 hover:bg-gray-100 rounded-full">
                          <MoreVertical className="h-4 w-4 text-gray-500" />
                        </button>

                        <button
                          className="p-1 hover:bg-gray-100 rounded-full"
                          onClick={(e) => HandleTrainerDelete(e, trainer.id)}
                        >
                          <Trash2 className="h-4 w-4 text-red-500" />
                        </button>

                        <button
                          className="p-1 hover:bg-gray-100 rounded-full"
                          onClick={(e) => {
                            e.stopPropagation();
                            setIsEditing(true);
                            setTrainerBeingEdited(trainer);
                            setNewTrainerName(trainer.name);
                            setNewTrainerEmail(trainer.email);
                            setNewTrainerRole(trainer.role);
                            setShowNewTrainerForm(true);
                          }}
                        >
                          <Edit2 className="h-4 w-4 text-gray-500" />
                        </button>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">
                      <div className="flex items-center gap-2 justify-end">
                        <span className="text-sm text-gray-600">
                          {Math.round(25)}%
                        </span>
                        <div className="w-24 h-2 bg-gray-200 rounded-full overflow-hidden">
                          <div
                            className="h-full bg-blue-600 rounded-full"
                            style={{ width: `${20}%` }}
                          />
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm text-gray-500">
                      {5} פעילים
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">
                      <span
                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          trainer.status === "active"
                            ? "bg-green-100 text-green-800"
                            : trainer.status === "inactive"
                            ? "bg-red-100 text-red-800"
                            : "bg-yellow-100 text-yellow-800"
                        }`}
                      >
                        {trainer.status.charAt(0).toUpperCase() +
                          trainer.status.slice(1)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">
                      <div className="text-sm text-gray-900">
                        {trainer.role === "senior_trainer"
                          ? "מאמן בכיר"
                          : trainer.role === "junior_trainer"
                          ? "מאמן זוטר"
                          : "תזונאי מומחה"}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center justify-end">
                        <div className="ml-4 text-right">
                          <div className="text-sm font-medium text-gray-900">
                            {trainer.name}
                          </div>
                          <div className="text-sm text-gray-500">
                            {trainer.email}
                          </div>
                        </div>
                        <img
                          src="/default-profile-image.png"
                          alt={trainer.name}
                          className="h-10 w-10 rounded-full object-cover"
                        />
                      </div>
                    </td>
                  </tr>
                );
              })}
          </tbody>
        </table>
      </div>

      {/* New Trainer Form Modal */}
      {showNewTrainerForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          {error && (
            <ErrorAlert
              message={
                (error as { data?: { message?: string } })?.data?.message ||
                "אירעה שגיאה"
              }
            />
          )}

          <div
            className="bg-white rounded-xl p-6 max-w-2xl w-full mx-4"
            dir="rtl"
          >
            <h3 className="text-xl font-semibold text-gray-800 mb-4">
              {isEditing ? "ערוך מאמן" : "הוסף מאמן חדש"}
            </h3>
            <form className="space-y-4" onSubmit={HandleTrainerSubmit}>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    שם
                  </label>
                  <input
                    type="text"
                    value={newTrainerName}
                    disabled={isLoading || isEditingLoading}
                    onChange={(e) => setNewTrainerName(e.target.value)}
                    className="w-full p-2 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 text-right"
                    placeholder="הזן שם מאמן"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    אימייל
                  </label>
                  <input
                    type="email"
                    value={newTrainerEmail}
                    disabled={isLoading || isEditingLoading}
                    onChange={(e) => setNewTrainerEmail(e.target.value)}
                    className="w-full p-2 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 text-right"
                    placeholder="הזן אימייל מאמן"
                  />
                </div>
              </div>

              <div className="rounded-lg">
                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                      <Phone className="h-4 w-4 text-gray-500 ml-2" />
                      מספר טלפון נייד{" "}
                      <span className="text-red-500 mr-1">*</span>
                    </label>
                    <div className="flex">
                      {/* Phone Number Input */}
                      <input
                        type="tel"
                        required
                        disabled={isLoading || isEditingLoading}
                        value={newTrainerMobileNumber}
                        onChange={(e) =>
                          setNewTrainerMobileNumber(
                            e.target.value.replace(/\D/g, "")
                          )
                        }
                        className="flex-1 h-10 px-3 border-2 border-gray-200 rounded-r-lg focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 text-right"
                        placeholder="50-1234567"
                        autoComplete="tel"
                      />
                      {/* Country Code Selector */}
                      <select
                        value={newTrainerCountryCode}
                        disabled={isLoading || isEditingLoading}
                        onChange={(e) =>
                          setNewTrainerCountryCode(e.target.value)
                        }
                        className="w-24 h-10 px-2 border-2 border-gray-200 rounded-l-lg focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 appearance-none"
                      >
                        <option value="+972">+972 🇮🇱</option>
                        <option value="+1">+1 🇺🇸</option>
                        <option value="+44">+44 🇬🇧</option>
                        <option value="+33">+33 🇫🇷</option>
                        <option value="+49">+49 🇩🇪</option>
                        <option value="+7">+7 🇷🇺</option>
                        <option value="+86">+86 🇨🇳</option>
                        <option value="+91">+91 🇮🇳</option>
                      </select>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      הזן מספר פעיל בוואטסאפ כדי לקבל עדכונים ולהישאר מחובר למסע
                      הכושר שלהם.
                    </p>
                  </div>
                  {/* Empty div to maintain grid layout */}
                  <div></div>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  תפקיד
                </label>
                <select
                  value={newTrainerRole}
                  disabled={isLoading || isEditingLoading}
                  onChange={(e) => setNewTrainerRole(e.target.value)}
                  className="w-full p-2 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 text-right"
                >
                  <option value="senior_trainer">מאמן בכיר</option>
                  <option value="junior_trainer">מאמן זוטר</option>
                  <option value="nutrition_specialist">תזונאי מומחה</option>
                </select>
              </div>

              <div className="flex justify-start gap-4">
                <button
                  type="submit"
                  disabled={isLoading || isEditingLoading}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center gap-2"
                >
                  {isLoading || isEditingLoading ? (
                    <Loader className="animate-spin h-5 w-5" />
                  ) : (
                    <Check className="h-5 w-5" />
                  )}
                  <span>{isEditing ? "שמור שינויים" : "הוסף מאמן"}</span>
                </button>

                <button
                  type="button"
                  onClick={() => setShowNewTrainerForm(false)}
                  className="px-4 py-2 text-gray-700 hover:text-gray-900"
                >
                  ביטול
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};
