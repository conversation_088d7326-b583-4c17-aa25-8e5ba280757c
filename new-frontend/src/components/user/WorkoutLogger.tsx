import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Play,
  Square,
  FastForward,
  CheckCircle,
  HelpCircle,
} from "lucide-react";
import {
  useAddWorkoutLogMutation,
  useAddWorkoutSetMutation,
  useGetTrainingPlanQuery,
} from "../../api/services/Trainee/TraineeService";
import { WorkoutSet } from "../../types/workout";
import { TrainingDay } from "../../types/training";
import Loader_1 from "../Loader/Loader_1";
import { ErrorAlert } from "../training/ErrorAlert";

// --------------------- Types ---------------------
interface WorkoutLoggerProps {
  userId: string;
  onWorkoutStart: () => void;
  onWorkoutEnd: () => void;
  onRestStart: (duration: number) => void;
  onPauseToggle: (paused: boolean) => void;
  restTimer: number;
  isPaused: boolean;
}

interface ActiveExercise {
  workoutExerciseId: string;
  name: string;
  sets: WorkoutSet[];
  currentSet: number;
  rest: string;
  instructions?: string;
  targetReps: string;
}

export const WorkoutLogger: React.FC<WorkoutLoggerProps> = ({
  userId,
  onWorkoutStart,
  onWorkoutEnd,
  onRestStart,
  onPauseToggle,
  restTimer,
  isPaused,
}) => {
  const { data, isLoading, refetch } = useGetTrainingPlanQuery({ userId });

  useEffect(() => {
    refetch();
  }, [refetch]);

  useEffect(() => {
    if (data) setTrainingPlan(data);
  }, [data]);

  const [trainingPlan, setTrainingPlan] = useState<TrainingDay[]>([]);
  const [selectedTrainingPlanId, setSelectedTrainingPlanId] =
    useState<string>("");
  const [selectedTrainingPlan, setSelectedTrainingPlan] =
    useState<TrainingDay | null>(null);

  useEffect(() => {
    if (selectedTrainingPlanId) {
      const existingPlan = trainingPlan.find(
        (plan) => plan.id === selectedTrainingPlanId
      );

      if (existingPlan) {
        setSelectedTrainingPlan(existingPlan);
      }
    }
  }, [selectedTrainingPlanId, trainingPlan]);

  const [addUserWorkoutLog, { error: addUserWorkoutLogError }] =
    useAddWorkoutLogMutation();

  const [workout, setWorkout] = useState<any>(null);
  const [activeExercise, setActiveExercise] = useState<ActiveExercise | null>(
    null
  );
  const [isWorkoutStarted, setIsWorkoutStarted] = useState(false);
  const [currentExerciseIndex, setCurrentExerciseIndex] = useState(0);
  const [workoutExerciseIds, setWorkoutExerciseIds] = useState<string[]>([]);
  const [autoFinish, setAutoFinish] = useState(false); // New state

  const getDateString = (): string => {
    const date = new Date();
    const yyyy = date.getFullYear();
    const mm = String(date.getMonth() + 1).padStart(2, "0");
    const dd = String(date.getDate()).padStart(2, "0");
    return `${yyyy}-${mm}-${dd}`;
  };

  const startWorkout = async () => {
    try {
      setAutoFinish(false);

      const date = getDateString();

      const resp = await addUserWorkoutLog({
        date,
        selectedTrainingPlanId,
      }).unwrap();

      if (!resp?.id) return;

      setWorkout(resp);

      // Use resp directly instead of workout (which won't be updated yet)
      const exercises = resp.exercises;

      if (
        selectedTrainingPlan &&
        Array.isArray(exercises) &&
        exercises.length > 0
      ) {
        const exerciseIds = exercises.map((item: any) => item.id);
        setWorkoutExerciseIds(exerciseIds);

        const firstExercise =
          selectedTrainingPlan.exercises[currentExerciseIndex];

        // Use Array.from to create unique set objects
        const numSets = parseInt(firstExercise.sets, 10) || 0;

        const sets = Array.from({ length: numSets }, () => ({
          weight: 0,
          reps: 0,
          completed: false,
        }));

        const workoutExerciseId = exerciseIds[currentExerciseIndex];

        setActiveExercise({
          workoutExerciseId,
          name: firstExercise.name,
          currentSet: 0,
          sets,
          targetReps: firstExercise.reps,
          rest: firstExercise.rest,
          instructions: firstExercise.instructions,
        });

        setIsWorkoutStarted(true);
        setCurrentExerciseIndex(0);
        onWorkoutStart();
      }
    } catch (error) {
      console.error("Error starting workout:", error);
    }
  };

  const [addWorkoutSet] = useAddWorkoutSetMutation();
  const [weight, setWeight] = useState("");
  const [reps, setReps] = useState("");
  const [showFinishConfirmation, setShowFinishConfirmation] = useState(false);

  const completeSet = async () => {
    if (!activeExercise || !weight || !reps) return;

    try {
      const currentWeight = parseFloat(weight);
      const currentReps = parseInt(reps);

      await addWorkoutSet({
        reps: currentReps,
        setNumber: activeExercise.currentSet + 1,
        weight: currentWeight,
        workoutExerciseId: activeExercise.workoutExerciseId,
      }).unwrap();

      // last set completed
      if (
        selectedTrainingPlan &&
        activeExercise.currentSet === activeExercise.sets.length - 1
      ) {
        // move to next exercise.
        if (currentExerciseIndex < (workout?.exercises.length || 0) - 1) {
          const restTime = parseInt(activeExercise.rest) || 60;

          onRestStart(restTime);

          const next = selectedTrainingPlan.exercises[currentExerciseIndex + 1];

          const sets = Array.from({ length: parseInt(next.sets) }, () => ({
            weight: 0,
            reps: 0,
            completed: false,
          }));

          setActiveExercise({
            workoutExerciseId: workoutExerciseIds[currentExerciseIndex + 1],
            name: next.name,
            sets,
            currentSet: 0,
            rest: next.rest,
            instructions: next.instructions,
            targetReps: next.reps,
          });

          setCurrentExerciseIndex((prev) => prev + 1);
          setWeight("");
          setReps("");
        }
        // workout finished.
        else {
          setAutoFinish(true);
          handleFinishWorkoutClick();
        }
      }
      // next set exists.
      else {
        const restTime = parseInt(activeExercise.rest) || 60;

        onRestStart(restTime);

        const updatedSets = [...activeExercise.sets];

        updatedSets[activeExercise.currentSet] = {
          weight: currentWeight,
          reps: currentReps,
          completed: true,
        };

        setActiveExercise({
          ...activeExercise,
          sets: updatedSets,
          currentSet: activeExercise.currentSet + 1,
        });

        setWeight("");
        setReps("");
      }
    } catch (error) {
      console.error("Error completing set:", error);
    }
  };

  const handleFinishWorkoutClick = async () => {
    setShowFinishConfirmation(true);
  };

  const skipExercise = async () => {
    try {
      if (!workout || currentExerciseIndex >= workout.exercises.length - 1) {
        handleFinishWorkoutClick();

        return;
      }

      // Move to next exercise
      if (selectedTrainingPlan) {
        const nextExercise =
          selectedTrainingPlan.exercises[currentExerciseIndex + 1];

        const nextWorkoutExerciseId =
          workoutExerciseIds[currentExerciseIndex + 1];

        const sets = Array.from(
          { length: parseInt(nextExercise.sets) },
          () => ({
            weight: 0,
            reps: 0,
            completed: false,
          })
        );

        setActiveExercise({
          workoutExerciseId: nextWorkoutExerciseId,
          name: nextExercise.name,
          sets,
          currentSet: 0,
          rest: nextExercise.rest,
          instructions: nextExercise.instructions,
          targetReps: nextExercise.reps,
        });

        onRestStart(0);
        setWeight("");
        setReps("");
        setCurrentExerciseIndex((prev) => prev + 1);
      }
    } catch (error) {
      console.error("Error skipping exercise:", error);
    }
  };

  const confirmFinishWorkout = async () => {
    try {
      setIsWorkoutStarted(false);
      setActiveExercise(null);
      setCurrentExerciseIndex(0);
      setWeight("");
      setReps("");
      setAutoFinish(false);
      setShowFinishConfirmation(false);
      onWorkoutEnd();
    } catch (error) {
      console.error("Error finishing workout:", error);
    }
  };

  const [showControlsHelp, setShowControlsHelp] = useState(false);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  const skipRest = () => onRestStart(0);

  const remainingSets = activeExercise
    ? activeExercise.sets.length - activeExercise.currentSet
    : 0;

  const remainingExercises = workout
    ? workout.exercises.length - currentExerciseIndex - 1
    : 0;

  // --------------------- UI (Loading) ---------------------
  if (isLoading) {
    return <Loader_1 />;
  }

  const backendToFrontendErrorMap: Record<string, string> = {
    "Only trainees can log workouts.": "רק מתאמנים יכולים לרשום אימונים.",
    "You've already completed a workout today.": "כבר השלמת אימון היום.",
    "Trainee does not have access to this training plan.":
      "אין לך גישה לתוכנית אימונים זו.",
  };

  const getWorkoutLogError = (backendMessage: string) => {
    console.log({ backendMessage });

    return (
      backendToFrontendErrorMap[backendMessage] ||
      "אירעה שגיאה בלתי צפויה. נסה שוב."
    );
  };

  // --------------------- UI (Workout NOT Started) ---------------------
  if (!isWorkoutStarted) {
    return (
      <div className="max-w-4xl mx-auto space-y-6" dir="rtl">
        {addUserWorkoutLogError && (
          <ErrorAlert
            message={getWorkoutLogError(
              (addUserWorkoutLogError as any)?.data?.message
            )}
          />
        )}
        <div className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl shadow-lg p-6 border-2 border-purple-700">
          <div className="flex items-center gap-3 mb-6">
            <div className="p-3 rounded-xl bg-purple-800">
              <Dumbbell className="h-6 w-6 text-purple-300" />
            </div>
            <h2 className="text-xl font-semibold text-gray-200">רישום אימון</h2>
          </div>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                בחר יום אימון
              </label>
              <select
                className="w-full p-2 border-2 border-gray-700 rounded-lg bg-gray-800 text-gray-200"
                value={selectedTrainingPlanId}
                onChange={(e) => setSelectedTrainingPlanId(e.target.value)}
              >
                <option value="">בחר יום</option>
                {trainingPlan.map((plan, i) => (
                  <option key={i} value={plan.id}>
                    {plan.day} - {plan.focus}
                  </option>
                ))}
              </select>
            </div>
            <button
              onClick={startWorkout}
              disabled={!selectedTrainingPlanId}
              className="w-full px-4 py-2 bg-purple-700 text-gray-200 rounded-lg hover:bg-purple-600 disabled:opacity-50"
            >
              התחל אימון
            </button>
          </div>
        </div>
      </div>
    );
  }

  // --------------------- UI (Workout Started / In Progress) ---------------------
  return (
    <div className="max-w-4xl mx-auto space-y-6" dir="rtl">
      <div className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl shadow-lg p-6 border-2 border-purple-700">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="p-3 rounded-xl bg-purple-800 transform hover:rotate-12 transition-transform duration-300">
              <Dumbbell className="h-6 w-6 text-purple-300" />
            </div>
            <h2 className="text-xl font-semibold text-gray-200">
              {selectedTrainingPlan?.day || "N/A"}
            </h2>
          </div>

          {/* ----------------------- REST TIMER ----------------------- */}
          {!autoFinish ? (
            restTimer > 0 && (
              <div className="flex items-center gap-4">
                {/* Skip Timer Button */}
                <button
                  onClick={skipRest}
                  className="flex items-center gap-2 bg-amber-800 px-4 py-2 rounded-lg hover:bg-amber-700 transition-colors duration-200 border border-amber-700"
                >
                  <FastForward className="h-5 w-5 text-amber-300" />
                  <span className="font-medium text-amber-200">
                    דלג על מנוחה
                  </span>
                </button>

                {/* Rest Timer Countdown*/}
                <div className="flex items-center gap-2 bg-amber-800 px-4 py-2 rounded-lg border border-amber-700">
                  <Timer className="h-5 w-5 text-amber-300" />
                  <span className="font-medium text-amber-200">
                    מנוחה: {formatTime(restTimer)}
                  </span>
                </div>
              </div>
            )
          ) : (
            <div
              dir="rtl"
              className="text-[0.9rem] text-amber-300 flex items-center justify-end gap-1"
            >
              <p className="text-amber-500 font-semibold">הערה :</p>
              <span>
                האימון שלך הסתיים. לחץ על כפתור "סיים אימון" כדי לסגור את מפגש
                האימון.
              </span>
            </div>
          )}
        </div>

        <div className="space-y-6">
          {/* Controls Help */}
          <div className="bg-gray-800 rounded-lg p-4 relative border border-gray-700">
            <button
              onClick={() => setShowControlsHelp(!showControlsHelp)}
              className=" p-2 rounded-full hover:bg-purple-900 transition-colors duration-200"
              title="עזרה"
            >
              <HelpCircle className="h-5 w-5 text-purple-400" />
            </button>
            {showControlsHelp && (
              <div className="space-y-3 mt-2">
                <h4 className="font-medium text-purple-300">כפתורי שליטה:</h4>
                <ul className="space-y-2 text-gray-300">
                  <li className="flex items-center gap-2">
                    <Square className="h-4 w-4 text-gray-400" /> השהה אימון -
                    עוצר את טיימר המנוחה ומאפשר הפסקה
                  </li>
                  <li className="flex items-center gap-2">
                    <Play className="h-4 w-4 text-green-400" /> המשך אימון -
                    ממשיך את האימון לאחר השהייה
                  </li>
                  <li className="flex items-center gap-2">
                    <SkipForward className="h-4 w-4 text-purple-400" /> דלג על
                    תרגיל - עבור לתרגיל הבא
                  </li>
                  <li className="flex items-center gap-2">
                    <FastForward className="h-4 w-4 text-amber-400" /> דלג על
                    מנוחה - מסיים את זמן המנוחה הנוכחי
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-red-400" /> סיים אימון
                    - משלים את האימון הנוכחי
                  </li>
                </ul>
              </div>
            )}
          </div>

          {/* Current Exercise */}
          <div className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-lg p-6 border border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-200">
                {(activeExercise && activeExercise?.name) || "N/A"}
              </h3>
              <div className="flex items-center gap-2">
                <button
                  disabled={autoFinish}
                  onClick={() => onPauseToggle(!isPaused)}
                  className={`p-2 rounded-lg transition-colors duration-200 ${
                    isPaused
                      ? "bg-green-800 hover:bg-green-700 disabled:bg-green-700 text-green-300 border border-green-700"
                      : "bg-amber-800 hover:bg-amber-700 disabled:bg-amber-700 text-amber-300 border border-amber-700"
                  }`}
                  title={isPaused ? "המשך אימון" : "השהה אימון"}
                >
                  {isPaused ? (
                    <Play className="h-5 w-5" />
                  ) : (
                    <Square className="h-5 w-5" />
                  )}
                </button>

                <button
                  onClick={skipExercise}
                  className="p-2 bg-purple-800 rounded-lg hover:bg-purple-700 disabled:bg-purple-700 transition-colors duration-200 border border-purple-700"
                  title="דלג על תרגיל"
                  disabled={autoFinish}
                >
                  <SkipForward className="h-5 w-5 text-purple-300" />
                </button>

                <button
                  onClick={handleFinishWorkoutClick}
                  className="p-2 bg-red-800 rounded-lg hover:bg-red-700 transition-colors duration-200 border border-red-700"
                  title="סיים אימון"
                >
                  <CheckCircle className="h-5 w-5 text-red-300" />
                </button>
              </div>
            </div>

            {/* Finish Workout Confirmation Dialog */}
            {showFinishConfirmation && (
              <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
                <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4 border-2 border-purple-700">
                  <h3 className="text-lg font-semibold text-gray-200 mb-4">
                    האם אתה בטוח שברצונך לסיים את האימון?
                  </h3>
                  <p className="text-gray-400 mb-6">
                    לא ניתן לשחזר את ההתקדמות לאחר סיום האימון.
                  </p>
                  <div className="flex justify-end gap-4">
                    <button
                      onClick={() => setShowFinishConfirmation(false)}
                      className="px-4 py-2 text-gray-400 hover:text-gray-300 transition-colors duration-200"
                    >
                      ביטול
                    </button>
                    <button
                      onClick={confirmFinishWorkout}
                      className="px-4 py-2 bg-red-700 text-gray-200 rounded-lg hover:bg-red-600 transition-colors duration-200"
                    >
                      סיים אימון
                    </button>
                  </div>
                </div>
              </div>
            )}

            {activeExercise &&
              activeExercise.currentSet === 0 &&
              activeExercise.instructions && (
                <div className="mb-4 p-4 bg-amber-900/40 rounded-lg text-amber-200 border border-amber-800/50">
                  {activeExercise.instructions}
                </div>
              )}

            {restTimer === 0 && (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      משקל (ק״ג)
                    </label>
                    <input
                      type="number"
                      disabled={autoFinish}
                      value={weight}
                      onChange={(e) => setWeight(e.target.value)}
                      className="w-full p-2 border-2 border-gray-700 bg-gray-800 rounded-lg text-gray-200 focus:border-purple-600 focus:ring focus:ring-purple-700 focus:ring-opacity-50"
                      placeholder="הכנס משקל"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      חזרות (יעד: {activeExercise?.targetReps})
                    </label>
                    <input
                      type="number"
                      disabled={autoFinish}
                      value={reps}
                      onChange={(e) => setReps(e.target.value)}
                      className="w-full p-2 border-2 border-gray-700 bg-gray-800 rounded-lg text-gray-200 focus:border-purple-600 focus:ring focus:ring-purple-700 focus:ring-opacity-50"
                      placeholder="הכנס חזרות"
                    />
                  </div>
                </div>

                <button
                  onClick={completeSet}
                  disabled={!weight || !reps || autoFinish}
                  className="w-full px-4 py-2 bg-purple-700 text-gray-200 rounded-lg hover:bg-purple-600 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed border border-purple-600"
                >
                  השלם סט
                </button>
              </div>
            )}

            <div className="mt-4 grid grid-cols-2 gap-4">
              <div className="bg-purple-800 p-3 rounded-lg border border-purple-700">
                <div className="text-sm text-purple-300">סטים שנותרו</div>
                <div className="text-lg font-semibold text-purple-200">
                  {autoFinish ? 0 : remainingSets}
                </div>
              </div>
              <div className="bg-green-800 p-3 rounded-lg border border-green-700">
                <div className="text-sm text-green-300">תרגילים שנותרו</div>
                <div className="text-lg font-semibold text-green-200">
                  {remainingExercises}
                </div>
              </div>
            </div>
          </div>

          {/* Next Exercise Preview */}
          {remainingExercises > 0 && workout && (
            <div className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-lg p-4 border border-gray-700">
              <div className="text-sm text-gray-400 mb-2">תרגיל הבא</div>
              <div className="font-medium text-gray-200">
                {workout.exercises[currentExerciseIndex + 1].name}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
